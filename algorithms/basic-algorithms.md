# 基础算法面试题

## 1. 数组相关算法

### 1.1 两数之和
**题目：** 给定一个整数数组和一个目标值，找出数组中和为目标值的两个数的索引。

**解题思路：**
1. **暴力解法**：双重循环，时间复杂度O(n²)
2. **哈希表优化**：一次遍历，时间复杂度O(n)

**实现描述：**
- 创建HashMap存储数值和索引的映射
- 遍历数组，对于每个元素，计算目标值与当前元素的差值
- 检查差值是否在HashMap中存在
- 如果存在，返回当前索引和HashMap中的索引
- 如果不存在，将当前元素和索引存入HashMap

**代码框架：**
```java
public int[] twoSum(int[] nums, int target) {
    Map<Integer, Integer> map = new HashMap<>();
    for (int i = 0; i < nums.length; i++) {
        int complement = target - nums[i];
        if (map.containsKey(complement)) {
            return new int[]{map.get(complement), i};
        }
        map.put(nums[i], i);
    }
    return new int[]{};
}
```

### 1.2 最大子数组和（Kadane算法）
**题目：** 给定一个整数数组，找到一个具有最大和的连续子数组。

**解题思路：**
- 动态规划思想
- 当前最大和要么是当前元素本身，要么是当前元素加上前面的最大和
- 维护一个全局最大值

**实现描述：**
- 初始化当前最大和为第一个元素
- 初始化全局最大和为第一个元素
- 从第二个元素开始遍历
- 对于每个元素，更新当前最大和为max(当前元素, 当前最大和+当前元素)
- 更新全局最大和为max(全局最大和, 当前最大和)

### 1.3 数组去重
**题目：** 给定一个排序数组，原地删除重复项，返回新长度。

**解题思路：**
- 双指针法
- 慢指针指向不重复元素的位置
- 快指针遍历数组
- 当快指针元素与慢指针元素不同时，慢指针前进并更新

**实现描述：**
- 如果数组为空，返回0
- 初始化慢指针为0
- 快指针从1开始遍历
- 当nums[fast] != nums[slow]时，slow++，然后nums[slow] = nums[fast]
- 返回slow + 1

## 2. 链表相关算法

### 2.1 反转链表
**题目：** 反转一个单链表。

**解题思路：**
1. **迭代法**：使用三个指针
2. **递归法**：递归到链表末尾再反转

**迭代实现描述：**
- 使用三个指针：prev（前一个节点）、curr（当前节点）、next（下一个节点）
- 初始化prev为null，curr为head
- 遍历链表，每次将curr的next指向prev
- 然后移动三个指针：prev = curr, curr = next, next = next.next

**递归实现描述：**
- 递归终止条件：当前节点为null或下一个节点为null
- 递归调用反转剩余部分
- 将当前节点的下一个节点的next指向当前节点
- 将当前节点的next设为null

### 2.2 合并两个有序链表
**题目：** 将两个升序链表合并为一个新的升序链表。

**解题思路：**
1. **迭代法**：使用虚拟头节点
2. **递归法**：递归比较节点值

**迭代实现描述：**
- 创建虚拟头节点dummy
- 使用指针current指向dummy
- 比较两个链表的当前节点值
- 将较小的节点连接到current后面
- 移动相应的指针
- 处理剩余的节点

### 2.3 链表中环的检测
**题目：** 给定一个链表，判断链表中是否有环。

**解题思路：**
- 快慢指针法（Floyd判圈算法）
- 慢指针每次移动一步，快指针每次移动两步
- 如果有环，快慢指针最终会相遇
- 如果没有环，快指针会到达链表末尾

**实现描述：**
- 初始化快慢指针都指向头节点
- 在循环中，慢指针移动一步，快指针移动两步
- 如果快指针或快指针的next为null，说明没有环
- 如果快慢指针相遇，说明有环

## 3. 字符串相关算法

### 3.1 最长公共前缀
**题目：** 编写一个函数来查找字符串数组中的最长公共前缀。

**解题思路：**
1. **垂直扫描**：逐个字符比较
2. **水平扫描**：两两比较前缀

**垂直扫描实现描述：**
- 以第一个字符串为基准
- 对于每个字符位置，检查所有字符串在该位置的字符是否相同
- 如果不同或某个字符串已经结束，返回当前前缀
- 如果相同，继续下一个字符

### 3.2 有效的括号
**题目：** 给定一个只包括括号的字符串，判断字符串是否有效。

**解题思路：**
- 使用栈数据结构
- 遇到左括号时入栈
- 遇到右括号时出栈并检查是否匹配

**实现描述：**
- 创建一个栈
- 遍历字符串中的每个字符
- 如果是左括号，入栈
- 如果是右括号，检查栈是否为空，为空则返回false
- 如果不为空，出栈并检查是否与当前右括号匹配
- 遍历结束后，检查栈是否为空

### 3.3 字符串转换整数
**题目：** 实现一个atoi函数，将字符串转换为整数。

**解题思路：**
- 处理空格、符号、数字、溢出等情况
- 状态机或条件判断

**实现描述：**
- 跳过前导空格
- 处理正负号
- 逐个字符转换数字
- 检查溢出情况
- 返回结果

## 4. 二叉树相关算法

### 4.1 二叉树的遍历
**题目：** 实现二叉树的前序、中序、后序遍历。

**解题思路：**
1. **递归法**：直接按照定义递归
2. **迭代法**：使用栈模拟递归过程

**前序遍历（根-左-右）：**
- 递归：先访问根节点，再递归左子树，最后递归右子树
- 迭代：使用栈，先将根节点入栈，每次出栈一个节点并访问，然后将其右子节点和左子节点依次入栈

**中序遍历（左-根-右）：**
- 递归：先递归左子树，再访问根节点，最后递归右子树
- 迭代：使用栈，先将所有左子节点入栈，然后出栈并访问，再处理右子树

**后序遍历（左-右-根）：**
- 递归：先递归左子树，再递归右子树，最后访问根节点
- 迭代：使用栈，类似前序遍历但顺序相反，最后反转结果

### 4.2 二叉树的最大深度
**题目：** 给定一个二叉树，找出其最大深度。

**解题思路：**
1. **递归法**：深度 = 1 + max(左子树深度, 右子树深度)
2. **层序遍历**：使用队列进行层序遍历，计算层数

**递归实现描述：**
- 如果节点为空，返回0
- 递归计算左子树深度
- 递归计算右子树深度
- 返回1 + max(左子树深度, 右子树深度)

### 4.3 对称二叉树
**题目：** 给定一个二叉树，检查它是否是镜像对称的。

**解题思路：**
- 递归比较左右子树
- 检查左子树的左子节点与右子树的右子节点是否相等
- 检查左子树的右子节点与右子树的左子节点是否相等

**实现描述：**
- 如果根节点为空，返回true
- 调用辅助函数比较左右子树
- 辅助函数接收两个节点参数
- 如果两个节点都为空，返回true
- 如果只有一个节点为空，返回false
- 如果两个节点值不相等，返回false
- 递归比较左节点的左子树与右节点的右子树，左节点的右子树与右节点的左子树

## 5. 动态规划相关算法

### 5.1 爬楼梯
**题目：** 假设你正在爬楼梯。需要n阶你才能到达楼顶。每次你可以爬1或2个台阶。有多少种不同的方法可以爬到楼顶？

**解题思路：**
- 动态规划：dp[i] = dp[i-1] + dp[i-2]
- 斐波那契数列变形

**实现描述：**
- 基础情况：dp[1] = 1, dp[2] = 2
- 对于第i阶，可以从第i-1阶爬1步，或从第i-2阶爬2步
- 所以dp[i] = dp[i-1] + dp[i-2]
- 可以使用滚动数组优化空间复杂度

### 5.2 最长递增子序列
**题目：** 给定一个无序的整数数组，找到其中最长上升子序列的长度。

**解题思路：**
1. **动态规划**：dp[i]表示以第i个元素结尾的最长递增子序列长度
2. **贪心+二分查找**：维护一个递增序列，使用二分查找优化

**动态规划实现描述：**
- 初始化dp数组，所有元素为1
- 对于每个位置i，遍历其前面的所有位置j
- 如果nums[j] < nums[i]，更新dp[i] = max(dp[i], dp[j] + 1)
- 返回dp数组中的最大值

### 5.3 背包问题
**题目：** 给定一个容量为W的背包和n个物品，每个物品有重量和价值，求背包能装入物品的最大价值。

**解题思路：**
- 动态规划：dp[i][w]表示前i个物品在容量为w时的最大价值
- 对于每个物品，选择装入或不装入

**实现描述：**
- 创建二维dp数组，dp[i][w]表示前i个物品在容量为w时的最大价值
- 初始化边界条件
- 对于每个物品i和容量w：
  - 如果当前物品重量大于容量，不能装入：dp[i][w] = dp[i-1][w]
  - 否则，选择装入或不装入的最大值：dp[i][w] = max(dp[i-1][w], dp[i-1][w-weight[i]] + value[i])

## 6. 排序算法

### 6.1 快速排序
**算法思想：**
- 分治法：选择一个基准元素，将数组分为两部分
- 小于基准的元素放在左边，大于基准的元素放在右边
- 递归排序左右两部分

**实现描述：**
- 选择基准元素（通常选择第一个、最后一个或中间元素）
- 使用双指针从两端向中间扫描
- 左指针找到大于基准的元素，右指针找到小于基准的元素
- 交换两个元素
- 继续扫描直到两指针相遇
- 将基准元素放到正确位置
- 递归排序左右两部分

### 6.2 归并排序
**算法思想：**
- 分治法：将数组分成两半，分别排序后合并
- 合并过程：比较两个已排序数组的元素，按顺序放入结果数组

**实现描述：**
- 如果数组长度小于等于1，直接返回
- 将数组分成两半
- 递归排序左半部分和右半部分
- 合并两个已排序的部分：
  - 使用两个指针分别指向两个数组的开始
  - 比较当前元素，将较小的放入结果数组
  - 移动相应指针，继续比较
  - 处理剩余元素

### 6.3 堆排序
**算法思想：**
- 利用堆的性质：大顶堆的根节点是最大值
- 建立大顶堆，然后依次取出堆顶元素

**实现描述：**
- 构建大顶堆：从最后一个非叶子节点开始，向上调整堆
- 排序过程：
  - 将堆顶元素与末尾元素交换
  - 减小堆的大小
  - 重新调整堆
  - 重复直到堆大小为1

## 7. 搜索算法

### 7.1 二分查找
**算法思想：**
- 在有序数组中查找目标值
- 每次比较中间元素，根据比较结果缩小搜索范围

**实现描述：**
- 初始化左右指针
- 在左指针小于等于右指针时循环：
  - 计算中间位置
  - 如果中间元素等于目标值，返回索引
  - 如果中间元素小于目标值，左指针移动到中间位置+1
  - 如果中间元素大于目标值，右指针移动到中间位置-1
- 如果没找到，返回-1

### 7.2 深度优先搜索（DFS）
**算法思想：**
- 从起始节点开始，沿着一条路径尽可能深地搜索
- 当无法继续时，回溯到上一个节点，尝试其他路径

**实现描述：**
- 标记当前节点为已访问
- 处理当前节点
- 对于当前节点的每个未访问的邻居：
  - 递归进行深度优先搜索
- 可以使用递归或栈来实现

### 7.3 广度优先搜索（BFS）
**算法思想：**
- 从起始节点开始，先访问所有邻居节点
- 然后再访问邻居节点的邻居节点，逐层扩展

**实现描述：**
- 创建队列，将起始节点入队
- 标记起始节点为已访问
- 当队列不为空时：
  - 出队一个节点
  - 处理该节点
  - 将该节点的所有未访问邻居入队并标记为已访问

## 8. 面试技巧

### 8.1 解题步骤
1. **理解题目**：仔细阅读题目，确认输入输出格式
2. **分析示例**：通过示例理解题目要求
3. **思考算法**：从简单到复杂，先想暴力解法再优化
4. **编写代码**：先写出大体框架，再填充细节
5. **测试验证**：用示例和边界情况验证代码
6. **优化分析**：分析时间空间复杂度，考虑优化方案

### 8.2 常见优化思路
1. **空间换时间**：使用哈希表、缓存等减少重复计算
2. **预处理**：提前计算部分结果，如前缀和
3. **双指针**：减少嵌套循环
4. **分治法**：将大问题分解为小问题
5. **贪心算法**：每步选择最优解
6. **动态规划**：避免重复计算子问题

### 8.3 时间复杂度分析
- **O(1)**：常数时间，如数组随机访问
- **O(log n)**：对数时间，如二分查找
- **O(n)**：线性时间，如遍历数组
- **O(n log n)**：如归并排序、快速排序
- **O(n²)**：如冒泡排序、选择排序
- **O(2^n)**：如递归求斐波那契数列

### 8.4 空间复杂度分析
- **O(1)**：常数空间，如简单变量
- **O(log n)**：如递归调用栈
- **O(n)**：如额外的数组或哈希表
- **O(n²)**：如二维数组

记住：算法题的核心是理解问题本质，选择合适的数据结构和算法，然后正确实现。多练习，总结模式，培养算法思维。