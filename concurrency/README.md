# 并发编程

## 知识点概览
- 线程基础概念
- 同步机制
- 线程池
- 并发工具类
- 锁机制
- 线程安全
- 死锁和解决方案

## 核心概念
### 线程创建
- 继承Thread类
- 实现Runnable接口
- 使用Callable和Future
- 线程池创建

### 同步机制
- synchronized关键字
- Lock接口
- ReentrantLock
- ReadWriteLock
- volatile关键字

### 并发工具
- CountDownLatch
- CyclicBarrier
- Semaphore
- BlockingQueue
- ConcurrentHashMap

## 常见面试题
- 线程的生命周期
- synchronized vs Lock的区别
- volatile的作用原理
- 线程池的工作原理
- 如何避免死锁？
- 什么是线程安全？