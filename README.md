# Java面试知识库

> 📚 全面的Java技术面试准备资料，涵盖从基础到高级的所有核心知识点

## 🎯 知识库特色

✨ **全面覆盖** - 从Java基础到大数据技术栈的完整面试准备  
🔍 **深度解析** - 每个知识点都有原理讲解和实战代码  
📱 **移动友好** - 支持手机阅读，随时随地学习  
🔄 **持续更新** - 根据最新面试趋势和技术发展更新内容  

## 📖 主要内容

### 🔐 认证与安全
- [JWT实现详解](authentication/jwt-implementation.md) - Spring Boot JWT完整实现方案

### 🗄️ 中间件技术
- [Redis实现原理](middleware/redis-implementation.md) - Redis数据结构与应用场景

### 📦 Java核心
- [Java集合框架](collections/java-collections-detailed.md) - 集合框架深度解析
- [面向对象编程](oop/README.md) - OOP核心概念

### 🗃️ 数据存储
- [MySQL+ElasticSearch协作](database/mysql-elasticsearch-integration.md) - 数据协作方案

### 🌸 Spring框架
- [Spring框架全解析](spring/spring-framework-comprehensive.md) - IOC、AOP、SpringCloud

### 📊 大数据分析
- [大数据技术栈](big-data/big-data-analysis-comprehensive.md) - Hadoop、Spark、Kafka等

### 🧮 算法与数据结构
- [基础算法面试题](algorithms/basic-algorithms.md) - 数组、链表、动态规划等

## 🚀 快速开始

### 方式一：在线阅读
- 📱 **手机端**：直接访问GitHub Pages网站
- 💻 **电脑端**：克隆仓库到本地

### 方式二：本地部署
```bash
# 克隆仓库
git clone https://github.com/wshuo/java_interview.git

# 进入目录
cd java_interview

# 本地服务器运行（需要安装docsify-cli）
npm i docsify-cli -g
docsify serve .
```

## 📋 学习路径

### 🎯 基础巩固（1-2周）
1. [Java集合框架](collections/java-collections-detailed.md) - HashMap、ArrayList等核心容器
2. [面向对象编程](oop/README.md) - 继承、封装、多态
3. [并发编程](concurrency/README.md) - 线程、同步、线程池

### 🚀 框架进阶（2-3周）
1. [Spring框架](spring/spring-framework-comprehensive.md) - IOC、AOP、SpringBoot
2. [JWT认证](authentication/jwt-implementation.md) - 安全认证实现
3. [Redis应用](middleware/redis-implementation.md) - 缓存和数据结构

### 🔥 高级应用（3-4周）
1. [数据存储协作](database/mysql-elasticsearch-integration.md) - 数据一致性
2. [大数据技术栈](big-data/big-data-analysis-comprehensive.md) - Spark、Kafka等
3. [算法与数据结构](algorithms/basic-algorithms.md) - 面试算法题

## 💡 使用技巧

### 🔍 搜索功能
- 使用右上角搜索框快速查找内容
- 支持中英文关键词搜索
- 可以搜索代码片段和方法名

### 📱 移动端优化
- 支持手机和平板阅读
- 左侧导航可收起，节省屏幕空间
- 代码块支持横向滚动

### 💾 离线使用
- 支持PWA，可以添加到手机桌面
- 缓存内容，支持离线阅读
- 自动更新最新内容

## 🎯 面试准备建议

### 📚 知识点复习
1. **按模块学习** - 先掌握单个知识点，再串联整体
2. **理论结合实践** - 每个概念都要能写出代码实现
3. **关注最新技术** - 了解行业发展趋势和新技术

### 🗣️ 面试技巧
1. **先说思路，再写代码** - 展示解题思维过程
2. **考虑边界情况** - 展示代码的健壮性
3. **主动优化** - 分析时间空间复杂度，提出优化方案

### 📝 项目经验准备
1. **技术选型理由** - 为什么选择这个技术
2. **遇到的问题** - 如何解决技术难题
3. **性能优化** - 具体的优化措施和效果

## 🤝 贡献指南

欢迎大家贡献内容，让这个知识库更加完善！

### 📝 内容贡献
- 添加新的面试题目和解答
- 完善现有内容的代码示例
- 更正错误或过时的信息

### 🐛 问题反馈
- 发现内容错误请提Issue
- 有好的建议请提PR
- 技术问题可以在Discussion中讨论

## 📞 联系方式

- 📧 邮箱：通过GitHub Issues联系
- 💬 讨论：GitHub Discussions
- 🔗 项目地址：[GitHub仓库](https://github.com/wshuo/java_interview)

## 📄 许可证

本项目采用 [MIT许可证](LICENSE)，欢迎学习和分享。

---

**💪 愿这个知识库能帮助你在Java面试中脱颖而出！**

> 💡 记住：面试不仅仅是考察知识点的记忆，更重要的是展示你的技术理解深度和解决问题的能力。加油！🚀