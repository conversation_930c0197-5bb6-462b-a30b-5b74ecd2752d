# 题目模板

## 基础模板

### 问题标题
**难度等级：** 初级/中级/高级  
**知识点：** 相关知识点标签  
**类型：** 概念题/编程题/设计题  

### 问题描述
清楚地描述问题，包括：
- 具体要求
- 输入输出（如果是编程题）
- 约束条件

### 答案解析
详细的答案解释，包括：
- 核心概念解释
- 实现思路
- 注意事项
- 可能的陷阱

### 代码示例
```java
// 提供相关的代码示例
public class Example {
    // 实现代码
}
```

### 扩展问题
- 相关的深入问题
- 变种问题
- 实际应用场景

### 参考资料
- 相关文档链接
- 推荐阅读材料

---

## 快速模板

### 问题：[问题标题]
**难度：** [级别] | **类型：** [类型] | **知识点：** [标签]

**问题描述：**
[问题内容]

**答案：**
[答案内容]

**代码：**
```java
[代码示例]
```

**要点：**
- [关键点1]
- [关键点2]
- [关键点3]