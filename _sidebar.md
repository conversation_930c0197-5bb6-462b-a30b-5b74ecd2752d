<!-- _sidebar.md -->

* [🏠 首页](/)
* [📋 知识库总结](面试知识库总结.md)

## 🔐 认证与安全
* [JWT实现详解](authentication/jwt-implementation.md)

## 🗄️ 中间件技术  
* [Redis实现原理](middleware/redis-implementation.md)

## 📦 Java核心
* [Java集合框架](collections/java-collections-detailed.md)
* [集合框架概览](collections/README.md)

## 🗃️ 数据存储
* [MySQL+ElasticSearch协作](database/mysql-elasticsearch-integration.md)

## 🌸 Spring框架
* [Spring框架全解析](spring/spring-framework-comprehensive.md)

## 📊 大数据分析
* [大数据技术栈](big-data/big-data-analysis-comprehensive.md)

## 🧮 算法与数据结构
* [基础算法面试题](algorithms/basic-algorithms.md)

## 🎯 基础知识
* [面向对象编程](oop/README.md)
* [并发编程](concurrency/README.md)

## 🛠️ 其他主题
* [设计模式](design-patterns/)
* [JVM原理](jvm/)
* [系统设计](system-design/)
* [项目模板](templates/)

---

**💡 使用提示:**
- 点击左上角菜单可收起/展开侧边栏
- 支持全文搜索，在右上角搜索框输入关键词
- 支持代码复制，点击代码块右上角按钮
- 移动端优化，支持手机阅读